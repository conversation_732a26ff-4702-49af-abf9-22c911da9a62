#include "main.h"
#include <map>
#include <string>
#include <iostream>

namespace iva
{
	/**
	 * @brief rtph264_depay src pad probe回调，用于过滤异常包
	 * @note  在rtph264_depay处理异常包后，阻止异常包发送到下游元素。
	 */
	static GstPadProbeReturn onRtpH264DepaySrcProbe(GstPad* pad, GstPadProbeInfo* info, gpointer user_data);

	GstElement* createSourceBin(guint index, guint process_id, guint device)
	{
		gchar elem_name[50];

		// src bin
		GST_ELEMENT_INDEX_NAME(elem_name, "src_bin", index);
		auto src_bin = gst_bin_new(elem_name);
		GST_ELEMENT_CHECK(src_bin, elem_name);
		ElementBin elementBin(src_bin);

		// updsrc
		GST_ELEMENT_INDEX_NAME(elem_name, "udp_src", index);
		auto udp_src = gst_element_factory_make("udpsrc", elem_name);
		GST_ELEMENT_CHECK(udp_src, elem_name);
		guint port = 30000 + index * 2 + (process_id-1) * totalChannelSize * 2;
		g_object_set(G_OBJECT(udp_src), "port", port, NULL);
		g_object_set(G_OBJECT(udp_src), "buffer-size", 25 * 1024 * 1024, NULL);
		elementBin.linkNext(udp_src);

		GST_ELEMENT_INDEX_NAME(elem_name, "udp_queue", index);
		auto udp_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(udp_queue, elem_name);
		elementBin.linkNext(udp_queue);

		// capsfilter
		GST_ELEMENT_INDEX_NAME(elem_name, "rtpdepay_capsfilter", index);
		auto rtpdepay_capsfilter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(rtpdepay_capsfilter, elem_name);
		auto enc_caps = gst_caps_new_simple("application/x-rtp",
			"media", G_TYPE_STRING, "video",
			"encoding-name", G_TYPE_STRING, "H264",
			"payload", G_TYPE_INT, 96,
			"clock-rate", G_TYPE_INT, 90000,
			NULL);
		g_object_set(G_OBJECT(rtpdepay_capsfilter), "caps", enc_caps, NULL);
		elementBin.linkNext(rtpdepay_capsfilter);

		// rtph264depay
		GST_ELEMENT_INDEX_NAME(elem_name, "rtph264_depay", index);
		auto rtph264_depay = gst_element_factory_make("rtph264depay", elem_name);
		GST_ELEMENT_CHECK(rtph264_depay, elem_name);
		elementBin.linkNext(rtph264_depay);

		/// h264Parse
		GST_ELEMENT_INDEX_NAME(elem_name, "h264parse%d", index);
		auto h264parse = gst_element_factory_make("h264parse", elem_name);
		GST_ELEMENT_CHECK(h264parse, elem_name);
		elementBin.linkNext(h264parse);

		// 初始化rtph264_depay错误状态标志
		g_object_set_data(G_OBJECT(rtph264_depay), "error-state", GINT_TO_POINTER(0));
		g_object_set_data(G_OBJECT(rtph264_depay), "channel-index", GINT_TO_POINTER(index));

		// 添加src pad probe来过滤异常包
		GstPad* src_pad = gst_element_get_static_pad(rtph264_depay, "src");
		if (src_pad)
		{
			gst_pad_add_probe(src_pad, (GstPadProbeType)(GST_PAD_PROBE_TYPE_BUFFER | GST_PAD_PROBE_TYPE_BUFFER_LIST), onRtpH264DepaySrcProbe, GINT_TO_POINTER(index), NULL);
			gst_object_unref(src_pad);
		}


		/**
		 * @brief aidecoder support auto detect output resolution, two way like below:
		 * 	1) set property, like 'aidecoder device-id=1, ..., ow=640, oh=384'
		 * 	2) add caps, like 'capsfilter video/x-raw(memory:mlu), width=640, height=384, format=NV12'
		 * 	if both two way is set, then the second manner(add caps) will cover the fist manner(set property)
		 */
		GST_ELEMENT_INDEX_NAME(elem_name, "decoder", index);
		auto decodebin = gst_element_factory_make(AI_DECODER, elem_name);
		GST_ELEMENT_CHECK(decodebin, elem_name);
		g_object_set(G_OBJECT(decodebin), "device-id", device, NULL);
		g_object_set(G_OBJECT(decodebin), "process-id", process_id, NULL);
		g_object_set(G_OBJECT(decodebin), "max-decode-channels", 50, NULL);	//channel count for one process
		g_object_set(G_OBJECT(decodebin), "input-buffer-num", 10, NULL);
		g_object_set(G_OBJECT(decodebin), "output-buffer-num", 15, NULL);
		{
			char buf[128] = { 0 };
			g_snprintf(buf, sizeof(buf), "idx:%02d,port:%d", index, port);
			g_object_set(G_OBJECT(decodebin), "debug-info", buf, NULL);
		}

		elementBin.linkNext(decodebin);

#ifdef CAMBRICON
		// queue，aidecoder out frame buffer is read only and not support thread async
		 GST_ELEMENT_INDEX_NAME(elem_name, "crop_queue", index);
		 auto crop_queue = gst_element_factory_make("queue", elem_name);
		 GST_ELEMENT_CHECK(crop_queue, elem_name);
		 elementBin.linkNext(crop_queue);


		// crop converter
		 g_snprintf(elem_name, sizeof(elem_name), "crop_conv%d", index);
		 auto crop_conv = gst_element_factory_make(AI_CONVERTER, elem_name);
		 GST_ELEMENT_CHECK(crop_conv, elem_name);
		 //elementBin.linkNext(crop_conv);
#endif

		// capsfilter
		g_snprintf(elem_name, sizeof(elem_name), "crop_capsfilter%d", index);
		auto crop_capsfilter = gst_element_factory_make("capsfilter", elem_name);
		GST_ELEMENT_CHECK(crop_capsfilter, elem_name);

		gchar caps_str[128];

#ifdef CAMBRICON_MLU370
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu),width=%d,height=%d,format=RGB", 640, 384);
#elif CAMBRICON_MLU270
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu),width=%d,height=%d,format=RGB", 640, 640);
#elif CAMBRICON_MLU220
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu),width=%d,height=%d,format=RGBA", 640, 640);
#else
        g_snprintf(caps_str, sizeof(caps_str), "video/x-raw(memory:mlu), width=%d, height=%d", 640, 384);
#endif
		auto crop_caps = gst_caps_from_string(caps_str);
		g_object_set(G_OBJECT(crop_capsfilter), "caps", crop_caps, NULL);
		//elementBin.linkNext(crop_capsfilter);

		GST_BIN_ADD_GHOST_PAD(src_bin, elementBin.last, "src");
		return src_bin;
	}
	static GstPadProbeReturn onRtpH264DepaySrcProbe(GstPad* pad, GstPadProbeInfo* info, gpointer user_data)
	{
		static std::map<int, int> dropCounters;  // 通道索引 -> 丢弃计数
		static constexpr int LOG_INTERVAL = 100;     // 每丢弃100个包打印一次日志
		int index = GPOINTER_TO_INT(user_data);

		// 获取关联的rtph264_depay元素
		GstElement* depay = GST_ELEMENT(gst_pad_get_parent_element(pad));
		if (!depay)
			return GST_PAD_PROBE_OK;

		bool dropPacket = false;
		std::string dropReason;
		// 检查rtph264_depay是否被标记为错误状态
		int errorState = GPOINTER_TO_INT(g_object_get_data(G_OBJECT(depay), "error-state"));
		if (errorState)
        {
			// 重置错误状态
			g_object_set_data(G_OBJECT(depay), "error-state", GINT_TO_POINTER(0));
			dropPacket = true;
			dropReason = "rtph264_depay报告错误";
		}

		// 释放元素引用
		gst_object_unref(depay);

		// 检查rtph264_depay的流状态
		auto lastFlowReturn = static_cast<GstStructure *>(g_object_get_data(G_OBJECT(pad), "last-flow-return"));
		// 如果有流错误，丢弃这个缓冲区
		if (lastFlowReturn)
        {
			// 清除流状态，为下一次检查准备
			g_object_set_data(G_OBJECT(pad), "last-flow-return", nullptr);
			if (lastFlowReturn)
				gst_structure_free(lastFlowReturn);

			dropPacket = true;
			dropReason = "流返回错误";
		}

		// 对于缓冲区本身的检查
        if (GST_PAD_PROBE_INFO_TYPE(info) & GST_PAD_PROBE_TYPE_BUFFER)
        {
            GstBuffer* buffer = GST_PAD_PROBE_INFO_BUFFER(info);

            // 检查缓冲区是否有效
            if (buffer && GST_BUFFER_FLAG_IS_SET(buffer, GST_BUFFER_FLAG_CORRUPTED))
            {
                dropPacket = true;
                dropReason = "缓冲区标记为已损坏";
            }
        }
        else if (GST_PAD_PROBE_INFO_TYPE(info) & GST_PAD_PROBE_TYPE_BUFFER_LIST)
        {
			GstBufferList* buffer_list = GST_PAD_PROBE_INFO_BUFFER_LIST(info);

			// 检查缓冲区列表是否有效
			if (buffer_list && gst_buffer_list_length(buffer_list) == 0)
            {
                dropPacket = true;
                dropReason = "空缓冲区列表";
			}
		}

		if (dropPacket)
        {
            dropCounters[index]++;
            if (dropCounters[index] % LOG_INTERVAL == 1)
            	std::cout << "通道" << index << ": 丢弃H264包(总计已丢弃" << dropCounters[index] << "个)" << std::endl;
                //IVA_LOG_WARN("通道{}: 丢弃H264包(总计已丢弃{}个), 原因: {}",index, dropCounters[index], dropReason);

            return GST_PAD_PROBE_DROP;
        }

		// 允许正常包通过
		return GST_PAD_PROBE_OK;
	}


}
