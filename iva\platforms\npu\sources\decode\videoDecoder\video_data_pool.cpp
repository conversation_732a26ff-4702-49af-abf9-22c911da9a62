#include <memory>
#include <cstdio>
#include <algorithm>
#include "video_data_pool.h"

namespace ai
{
    VideoDataPool::~VideoDataPool() = default;

    void VideoDataPool::destroy()
    {
        std::lock_guard<std::mutex> ol(lock_);
        freeList_.clear();
        usingList_.clear();

        for (const auto ptr : dataList_)
            freebuf(ptr);
        dataList_.clear();
    }

    bool VideoDataPool::release(void *buf)
    {
        if (!buf)
            return false;

        std::lock_guard<std::mutex> lock(lock_);
        if (dataList_.find(buf) == dataList_.end())
        {
            AIVCODEC_ERROR("data pool release failed: data %p not found in dataList_", buf);
            return false;
        }

        usingList_.remove(buf);
        freeList_.push_back(buf);

        return true;
    }

    bool VideoDataPool::isRelease(const void *buff)
    {
        std::lock_guard ol(lock_);
        const bool isUsed = std::any_of(usingList_.begin(), usingList_.end(), [buff](const void *data) {
            return data == buff;
        });
        return !isUsed;
    }

    void *VideoDataPool::acquire()
    {
        std::lock_guard<std::mutex> ol(lock_);
        // 从 freeList_ 复用空闲缓存
        if (!freeList_.empty())
        {
            void *data = freeList_.front();
            freeList_.pop_front();

            usingList_.emplace_back(data);

            return data;
        }

        // 已达最大缓存限制
        if (usingList_.size() >= static_cast<std::size_t>(maxSize_))
        {
            auto now = std::chrono::steady_clock::now();
            if (now - lastLogTime > std::chrono::minutes (30))
            {
                AIVCODEC_ERROR("data pool is full! maxSize: %u , front %p, back %p",maxSize_,  usingList_.front() , usingList_.back());
                lastLogTime = now;
            }
            return nullptr;
        }

        // 分配新缓存
        void* data = allocbuf();
        if (!data)
        {
            AIVCODEC_ERROR("data pool allocbuf failed!");
            return nullptr;
        }

        usingList_.emplace_back(data);
        dataList_.insert(data);

        return data;
    }

    void* VDecDataPool::forceRecycleOldBuffer(const long currentFrameNum)
    {
        std::lock_guard ol(lock_);
        if (usingList_.size() >= static_cast<std::size_t>(maxSize_))
        {
            // 检查最早的BufferDesc
            void* oldestBuffer = usingList_.front();
            const auto bufferDesc = static_cast<BufferDesc*>(oldestBuffer);

            if (!bufferDesc)
            {
                usingList_.remove(bufferDesc);
                return nullptr;
            }

            // 检查帧序号差距
            const long frameDiff = currentFrameNum - bufferDesc->frameNum_;
            if (frameDiff >= FORCE_RECYCLE_FRAME_THRESHOLD)
            {
                // 强制回收：从usingList_移除，放入freeList_
                usingList_.remove(oldestBuffer);
                freeList_.push_back(oldestBuffer);

                AIVCODEC_WARN("Force recycled buffer with frame %ld (current: %ld, diff: %ld)", bufferDesc->frameNum_, currentFrameNum, frameDiff);
                return oldestBuffer;
            }
        }
        return nullptr;
    }

    BufferDesc* VDecDataPool::findBufferDescByPts(const int64_t pts)
    {
        std::lock_guard ol(lock_);
        for (const auto buffer : usingList_)
        {
            const auto bufferDesc = static_cast<BufferDesc*>(buffer);
            if (bufferDesc && bufferDesc->pkt_.pts_ == pts)
                return bufferDesc;
        }
        AIVCODEC_ERROR("VDecDataPool::findBufferDesc failed: buffer not found for pts %ld", pts);
        return nullptr;
    }

    void* DeviceFramePool::forceRecycleOldBuffer(long currentFrameNum)
    {
        std::lock_guard ol(lock_);
        if (usingList_.size() >= static_cast<std::size_t>(maxSize_))
        {
            // 检查最早的device Frame
            void* oldestBuffer = usingList_.front();
            const auto deviceFrame = static_cast<DeviceFrame*>(oldestBuffer);

            if (!deviceFrame)
            {
                usingList_.remove(deviceFrame);
                return nullptr;
            }

            // 检查帧序号差距
            const long frameDiff = currentFrameNum - deviceFrame->frame_num;
            if (frameDiff >= FORCE_RECYCLE_FRAME_THRESHOLD)
            {
                // 强制回收：从usingList_移除，放入freeList_
                usingList_.remove(oldestBuffer);
                freeList_.push_back(oldestBuffer);

                AIVCODEC_WARN("Force recycled device buffer with frame %ld (current: %ld, diff: %ld)", deviceFrame->frame_num, currentFrameNum, frameDiff);
                return oldestBuffer;
            }
        }
        return nullptr;
    }

    /**
     * @brief   获取当前缓存池中缓存数量
     * @return  已使用内存大小和总内存池大小
     */
    std::pair<uint32_t, uint32_t> VideoDataPool::currentSize()
    {
        std::lock_guard<std::mutex> ol(lock_);
        return std::make_pair(usingList_.size(), dataList_.size());
    }



    // VDecDataPool
    void *VDecDataPool::allocbuf() // 子类需要实现实际缓存创建和销毁方式
    {
        #ifdef HUAWEI_310
            if (option_ == nullptr) {
                return nullptr;
            }

            BufferDesc * buf = nullptr;
            acldvppStreamDesc* sdesc(NULL);
            acldvppPicDesc* fdesc(NULL);

            do {
                CHECK_AIF_ERR_BK(nullptr == (sdesc = acldvppCreateStreamDesc()), VLOGFMT "acldvppCreateStreamDesc failed !", VLOGPARAM)
                CHECK_ACL_ERR_BK(acldvppSetStreamDescEos(sdesc, 0))
                CHECK_ACL_ERR_BK(acldvppSetStreamDescRetCode(sdesc, ACL_SUCCESS))
                CHECK_ACL_ERR_BK(acldvppSetStreamDescFormat(sdesc, getEncFmt(option_)))

                CHECK_AIF_ERR_BK(nullptr == (fdesc = acldvppCreatePicDesc()), "acldvppCreatePicDesc failed !")
                CHECK_ACL_ERR_BK(acldvppSetPicDescRetCode(fdesc, ACL_SUCCESS))
                CHECK_ACL_ERR_BK(acldvppSetPicDescFormat(fdesc, ai::PixelFmt::NV21 == option_->pixFmt_ ? PIXEL_FORMAT_YVU_SEMIPLANAR_420 : PIXEL_FORMAT_YUV_SEMIPLANAR_420))
                CHECK_ACL_ERR_BK(acldvppSetPicDescWidth(fdesc, option_->oWidth_))
                CHECK_ACL_ERR_BK(acldvppSetPicDescHeight(fdesc, option_->oHeight_))
                CHECK_ACL_ERR_BK(acldvppSetPicDescWidthStride(fdesc, ALIGN_UP16(option_->oWidth_)))
                CHECK_ACL_ERR_BK(acldvppSetPicDescHeightStride(fdesc, ALIGN_UP2(option_->oHeight_)))

                buf = new BufferDesc();
                buf->picDesc_ = fdesc;
                buf->streamDesc_ = sdesc;
                return buf;
            } while (false);

            DELETE_OBJECT(buf);
            CHECK_ACL_FREE(fdesc, acldvppDestroyPicDesc(fdesc))
            CHECK_ACL_FREE(sdesc, acldvppDestroyStreamDesc(sdesc))
            return nullptr;
        #else
            return new BufferDesc();
        #endif
    }

    void VDecDataPool::freebuf(void *data)
    {
        auto desc = static_cast<BufferDesc*>(data);
        if (!desc)
            return;

        #ifdef DEV_HUAWEI
            CHECK_ACL_FREE(desc->pkt_.buf_, devFree(desc->pkt_.buf_))
        #endif

        #ifdef HUAWEI_310
            CHECK_ACL_FREE(desc->picDesc_, acldvppDestroyPicDesc(desc->picDesc_))
            CHECK_ACL_FREE(desc->streamDesc_, acldvppDestroyStreamDesc(desc->streamDesc_))
        #endif

        delete desc;
        desc = nullptr;
    }

    void VDecDataPool::insertBufferDesc(BufferDesc *desc)
    {
        if (nullptr == desc || nullptr == desc->pkt_.buf_)
            return;

        std::lock_guard<std::mutex> lock(getLock());
        auto frameNum = static_cast<uint64_t>(desc->frameNum_);
        auto [iter, inserted] = frameList_.emplace(frameNum, desc);
        if (!inserted)
            AIVCODEC_ERROR("insertBufferDesc failed: frameNum %llu already exists in frameList_", frameNum);
    }

    BufferDesc *VDecDataPool::getAndEraseBufferDesc(uint64_t frameNum)
    {
        std::lock_guard<std::mutex> lock(getLock());
        auto iter = frameList_.find(frameNum);
        if (iter != frameList_.end())
        {
            auto desc = static_cast<BufferDesc*>(iter->second);
            frameList_.erase(iter);
            return desc;
        }
        else
        {
            AIVCODEC_DEBUG(" getAndEraseBufferDesc failed: frameNum %llu not found in frameList", frameNum);
            return nullptr;
        }
    }

    void VDecDataPool::getAndClearTmpList(std::list<void*> & outList)
    {
        std::lock_guard<std::mutex> ol(getLock());

        for (auto& [key, value] : frameList_)
            outList.push_back(value);

        frameList_.clear();
    }



    // NPU设备帧内存分配释放管理
    bool DeviceFramePool::setDeviceId(const int device)
    {
        int32_t curDevice(-1);
        
        if (devGetDevId(&curDevice) != Dev_RET_SUCCESS || curDevice != device)
        {
            if (devSetDevId(device) != Dev_RET_SUCCESS)
            {
                AIVCODEC_ERROR("DeviceFramePool::setDeviceId  failed ! curDevId:%d, memDevId:%d", curDevice, device);
                return false;
            }
        }
        return true;
    }

    void* DeviceFramePool::allocDeviceBuf(const int device, const int size)
    {
        DeviceFramePool::setDeviceId(device);
        void* devBuf = nullptr;
        auto ret = devMallocWithId(device, &devBuf, size);
        if (ret != Dev_RET_SUCCESS)
        {
            AIVCODEC_ERROR("DeviceFramePool::allocDeviceBuf failed, ret %d", ret);
            return nullptr;
        }

        AIVCODEC_DEBUG("DeviceFramePool::allocDeviceBuf size: %d, buf:0x%x", size, devBuf);
        return devBuf;
    }

    bool DeviceFramePool::freeDeviceBuf(const int device, void* buf)
    {
        DeviceFramePool::setDeviceId(device);
        AIVCODEC_DEBUG("DeviceFramePool::freeDeviceBuf buf:0x%x", buf);
        auto ret = devFree(buf);
        if (ret != Dev_RET_SUCCESS)
        {
            AIVCODEC_ERROR("DeviceFramePool::freeDeviceBuf failed, ret %d", ret);
            return false;
        }

        return true;
    }

    void *DeviceFramePool::allocbuf()
    {
        auto frame = std::make_unique<DeviceFrame>();
        if (devBufAllocSize_ > 0)
        {
            void* devBuf = DeviceFramePool::allocDeviceBuf(device_, devBufAllocSize_);
            if (!devBuf)
            {
                AIVCODEC_ERROR("DeviceFramePool::allocbuf alloc devive mem %d failed", devBufAllocSize_);
                return nullptr;
            }
            frame->ptrs[0] = devBuf;
        }
        else if(width_ > 0 && height_ > 0)
        {
            size_t len0 = width_ * height_;
            size_t len1 = len0 / 2;

            void* devBuf = DeviceFramePool::allocDeviceBuf(device_, len0);
            if (!devBuf)
            {
                AIVCODEC_ERROR("DeviceFramePool::allocbuf alloc devive mem %d failed", len0);
                return nullptr;
            }
            frame->ptrs[0] = devBuf;

            devBuf = DeviceFramePool::allocDeviceBuf(device_, len1);
            if (nullptr == devBuf)
            {
                AIVCODEC_ERROR("DeviceFramePool::allocbuf alloc devive mem %d failed", len1);
                DeviceFramePool::freeDeviceBuf(device_, frame->ptrs[0]);
                return nullptr;
            }
            frame->ptrs[1] = devBuf;
        }

        return frame.release();
    }

    void DeviceFramePool::freebuf(void *data)
    {
        auto devFrame = (DeviceFrame *)data;
        if (!devFrame)
            return;

        if (devFrame->ptrs[0])
        {
            DeviceFramePool::freeDeviceBuf(device_, devFrame->ptrs[0]);
            devFrame->ptrs[0] = nullptr;
        }
        if (devFrame->ptrs[1])
        {
            DeviceFramePool::freeDeviceBuf(device_, devFrame->ptrs[1]);
            devFrame->ptrs[1] = nullptr;
        }

        memset(devFrame->ptrs, 0, sizeof(devFrame->ptrs));
        memset(devFrame->strides, 0, sizeof(devFrame->strides));
        delete devFrame;
    }
}
