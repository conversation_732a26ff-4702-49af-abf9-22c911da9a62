#pragma once
#include <gst/gst.h>

#define AI_TRACKER "aitracker"
#define AI_CONVERTER "aivideoconverter"
#define AI_STREAMMUX "aistreammux"
#define AI_STREAMDEMUX "aistreamdemux"
#define AI_INFER "aiinfer"

#ifdef CAMBRICON
#ifdef CAMBRICON_MLU370
#define SOFTWARE_ENCODER
#define AI_DECODER "aivideodecoder"
#define AI_ENCODER "aivideoencoder"
#else
#define SOFTWARE_ENCODER
#define AI_DECODER "aivideodecoder"
#define AI_ENCODER "x264enc"
#endif
#elif HUAWEI
#define SOFTWARE_ENCODER
#define AI_DECODER "aivideodecoder"
#define AI_ENCODER "x264enc"
#endif

#define GST_ERR_MSG_V(msg, ...) \
    g_print("** ERROR: <%s:%d>: " msg "\n", __func__, __LINE__, ##__VA_ARGS__)

#define GST_ELEMENT_INDEX_NAME(elem_name, name, index) \
	g_snprintf(elem_name, sizeof(elem_name), name"%d", index) \

#define GST_ELEMENT_CHECK(elem, elem_name) \
	if (!elem) \
	{	\
		GST_ERR_MSG_V("Failed to create '%s'", elem_name);	\
		return NULL;	\
	}	\

#define GST_ELEMENT_CHECK1(elem, elem_name) \
	if (!elem) \
	{	\
		GST_ERR_MSG_V("Failed to create '%s'", elem_name);	\
		return -1;	\
	}	\

#define CHECK_IF_MSG1(cond, msg) \
	if (!cond) \
	{	\
		GST_ERR_MSG_V(msg);	\
		return -1;	\
	}	\

#define GST_ELEMENT_LINK_MANY_CHECK(group_name, element_1, element_2, ...) \
	if (!gst_element_link_many(element_1, element_2,  ## __VA_ARGS__, NULL)) \
	{	\
		g_printerr("[%s] elements could not be linked. Exiting.\n", group_name); \
		return NULL; \
	} \

#define GST_LINK_ELEMENT_FULL(elem1, elem1_pad_name, elem2, elem2_pad_name) \
  do { \
    GstPad *elem1_pad = gst_element_get_static_pad(elem1, elem1_pad_name); \
    GstPad *elem2_pad = gst_element_get_static_pad(elem2, elem2_pad_name); \
    GstPadLinkReturn ret = gst_pad_link (elem1_pad,elem2_pad); \
    if (ret != GST_PAD_LINK_OK) { \
      gchar *n1 = gst_pad_get_name (elem1_pad); \
      gchar *n2 = gst_pad_get_name (elem2_pad); \
      GST_ERR_MSG_V ("Failed to link '%s' and '%s': %d", \
          n1, n2, ret); \
      g_free (n1); \
      g_free (n2); \
      gst_object_unref (elem1_pad); \
      gst_object_unref (elem2_pad); \
      break; \
    } \
    gst_object_unref (elem1_pad); \
    gst_object_unref (elem2_pad); \
  } while (0)

#define GST_BIN_ADD_GHOST_PAD_NAMED(bin, elem, pad, ghost_pad_name) \
  do { \
    GstPad *gstpad = gst_element_get_static_pad (elem, pad); \
    if (!gstpad) { \
      GST_ERR_MSG_V ("Could not find '%s' in '%s'", pad, \
          GST_ELEMENT_NAME(elem)); \
      break; \
    } \
    gst_element_add_pad (bin, gst_ghost_pad_new (ghost_pad_name, gstpad)); \
    gst_object_unref (gstpad); \
  } while (0)

#define GST_BIN_ADD_GHOST_PAD(bin, elem, pad) \
      GST_BIN_ADD_GHOST_PAD_NAMED (bin, elem, pad, pad)

inline gboolean
link_element_to_streammux_sink_pad(GstElement * streammux, GstElement * elem, gint index)
{
	gboolean ret = FALSE;
	GstPad* mux_sink_pad = NULL;
	GstPad* src_pad = NULL;
	gchar pad_name[16];

	if (index >= 0) {
		g_snprintf(pad_name, 16, "sink_%u", index);
		pad_name[15] = '\0';
	}
	else {
		strcpy(pad_name, "sink_%u");
	}

	mux_sink_pad = gst_element_get_request_pad(streammux, pad_name);
	if (!mux_sink_pad) {
		GST_ERR_MSG_V("Failed to get sink pad from streammux");
		goto done;
	}

	src_pad = gst_element_get_static_pad(elem, "src");
	if (!src_pad) {
		GST_ERR_MSG_V("Failed to get src pad from '%s'",
			GST_ELEMENT_NAME(elem));
		goto done;
	}

	if (gst_pad_link(src_pad, mux_sink_pad) != GST_PAD_LINK_OK) {
		GST_ERR_MSG_V("Failed to link '%s' and '%s'", GST_ELEMENT_NAME(streammux),
			GST_ELEMENT_NAME(elem));
		goto done;
	}

	ret = TRUE;

done:
	if (mux_sink_pad) {
		gst_object_unref(mux_sink_pad);
	}
	if (src_pad) {
		gst_object_unref(src_pad);
	}
	return ret;
}

inline gboolean
link_element_to_demux_src_pad(GstElement * demux, GstElement * elem, guint index)
{
	gboolean ret = FALSE;
	GstPad* demux_src_pad = NULL;
	GstPad* sink_pad = NULL;
	gchar pad_name[16];

	g_snprintf(pad_name, 16, "src_%02d", index);
	demux_src_pad = gst_element_get_request_pad(demux, pad_name);
	GST_LINK_ELEMENT_FULL(demux, pad_name, elem, "sink");

	ret = TRUE;

	if (demux_src_pad) {
		gst_object_unref(demux_src_pad);
	}
	if (sink_pad) {
		gst_object_unref(sink_pad);
	}
	return ret;
}

extern GstElement* pipeline;
namespace iva
{
	struct ElementBin
	{
		GstElement* bin = nullptr;
		GstElement* first = nullptr;
		GstElement* last = nullptr;
		ElementBin(GstElement* gstbin) { bin = gstbin; }

		GstElement* linkNext(GstElement* next)
		{
			gst_bin_add_many(GST_BIN(bin), next, NULL);
			if (last) GST_ELEMENT_LINK_MANY_CHECK(next->object.name, last, next);
			if (!first) first = next;

			last = next;
			return last;
		}
	};

	GstElement* createSourceBin(guint index, guint process_id, guint device= 0);
	GstElement* createSinkBin(guint index, guint device= 0);
	GstElement* createCoreBin(guint index, guint device= 0);
	GstElement* createFakeSink(guint index);
	GstElement* createEncodeBin(guint device= 0);

	bool switchChannel(int channel);
}

/** Defines IVA target types. */
typedef enum
{
    IVA_TARGET_TYPE_PERSON = 0,
    IVA_TARGET_TYPE_CAR = 1,
    IVA_TARGET_TYPE_BUS = 2,
    IVA_TARGET_TYPE_TRUCK = 3,
    IVA_TARGET_TYPE_TWO_WHEEL = 4,

    IVA_TARGET_TYPE_ROADBLOCK =10,
    IVA_TARGET_TYPE_FIRE =11,
    IVA_TARGET_TYPE_SMOKE =12,
    IVA_TARGET_TYPE_LIGHT =13,
	IVA_TARGET_TYPE_THROWAYAY
} IVATargetType;

/**
 * @brief 总线消息监听回调，用于捕获rtph264_depay产生的错误
 * @note  当rtph264_depay产生错误消息时，将元素标记为错误状态
 */
GstBusSyncReply onBusMessageSync(GstBus* bus, GstMessage* message, gpointer user_data);

extern int totalChannelSize;
extern int processId;
extern int deviceId;
